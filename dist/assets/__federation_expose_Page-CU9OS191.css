
.plugin-page[data-v-bd8f5cd0] {
  margin: 0 auto;
}

/* 使卡片等宽并适应移动端 */
.d-flex.flex-wrap[data-v-bd8f5cd0] {
  gap: 16px;
}

/* 移动端堆叠布局 */
@media (max-width: 768px) {
.d-flex.flex-wrap[data-v-bd8f5cd0] {
    flex-direction: column;
}
}
.drag-handle[data-v-bd8f5cd0] {
  cursor: move;
}
.toggle-container[data-v-bd8f5cd0] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.625rem;
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.subscription-card[data-v-bd8f5cd0] {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  background: white;
}
.subscription-card[data-v-bd8f5cd0]:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}
.card-title[data-v-bd8f5cd0] {
  color: whitesmoke;
}
.card-header[data-v-bd8f5cd0] {
  padding: 0.625rem;
  background: linear-gradient(135deg, rgba(var(--v-theme-primary), 1) 0%, rgba(var(--v-theme-primary), 0.7) 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-refresh-button[data-v-bd8f5cd0] {
  background-color: rgba(var(--v-theme-primary), 0.9);
  color: whitesmoke;
  border: none;
  border-radius: 6px;
  padding: 0.625rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}
.search-field[data-v-bd8f5cd0] {
  max-width: 25rem;
}
.clash-data-table[data-v-bd8f5cd0] {
  max-height: 40rem;
  overflow-y: auto;
}

